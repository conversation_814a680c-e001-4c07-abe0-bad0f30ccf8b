flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.699054] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.701113] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.702808] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.704913] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T07:32:46.809838] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T07:32:46.810256] [PerformanceMonitoringService] Slow frame detected {"duration_ms":400}
flutter: ❌ ERROR [2025-07-19T07:32:46.813121] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.817635] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T07:32:46.823501] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T07:32:46.825068] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-19T07:32:46.853227] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:46.885305] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:46.986732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.296989] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.318378] [PerformanceMonitoringService] Slow frame detected {"duration_ms":21}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.412509] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.452261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T07:32:47.751314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.810177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.835449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.903011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:47.951568] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:48.002237] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:48.051972] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🐛 DEBUG [2025-07-19T07:32:48.202031] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:48.815249] [PerformanceMonitoringService] Slow frame detected {"duration_ms":63}
flutter: 🐛 DEBUG [2025-07-19T07:32:48.835553] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T07:32:49.880798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T07:32:49.968846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.052377] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.085086] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.119077] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.168432] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.924549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-19T07:32:50.951859] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: ⚠️ WARNING [2025-07-19T07:32:51.288326] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.408814] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.435020] [PerformanceMonitoringService] Slow frame detected {"duration_ms":25}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.468196] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.535248] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.619282] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.685610] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.752764] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.819799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:51.902224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.045882] [PerformanceMonitoringService] Slow frame detected {"duration_ms":144}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.069920] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.121635] [PerformanceMonitoringService] Slow frame detected {"duration_ms":53}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.236177] [PerformanceMonitoringService] Slow frame detected {"duration_ms":113}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.270122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":34}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.335517] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.385052] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.468948] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.535224] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.602324] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.669603] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.735735] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.802708] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.868366] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:52.936399] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.002829] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.093488] [PerformanceMonitoringService] Slow frame detected {"duration_ms":91}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.136038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.202251] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.285785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.353058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.436375] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.503246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.586348] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.619398] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.686240] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.769261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.835649] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.902917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:53.952176] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.021310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":69}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.068671] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.135876] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.202373] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.268804] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.335089] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.403170] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.469579] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.518445] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.568833] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.652956] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.719308] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.762013] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.852443] [PerformanceMonitoringService] Slow frame detected {"duration_ms":89}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.903636] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:54.969139] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.019501] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.086550] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.152089] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.202633] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.286565] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.335258] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:55.402726] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:57.786112] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:57.835630] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:57.905472] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:57.969459] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:58.018974] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:58.086769] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:58.135085] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:58.186065] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.054928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.102322] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.151706] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.218533] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.269474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.352244] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.419544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.486158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.535677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.602444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.652310] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.719358] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.786466] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.853654] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.902467] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:32:59.968634] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.053824] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.118423] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.186245] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.235559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.302512] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.369200] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.435460] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.502874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.569615] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.636163] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.685290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.753797] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.802396] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.869353] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.935903] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:00.969448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.035790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.101714] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.169330] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.202024] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.285670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: ⚠️ WARNING [2025-07-19T07:33:01.315861] [PerformanceMonitoringService] High memory usage detected {"memory_mb":165.0}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.352746] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.419867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.451966] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.519819] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.586505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.635981] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.685436] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.752180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.819006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.869005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.936277] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:01.986159] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.035596] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.103221] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.135502] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.536295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.586622] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.635273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.701677] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.752286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.843549] [PerformanceMonitoringService] Slow frame detected {"duration_ms":91}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.869199] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.919096] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:02.986271] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.035977] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.068686] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.135893] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.203286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.268960] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.301752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.369758] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.419465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.485161] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.553073] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.586038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.619543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:03.668339] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-19T07:33:06.288983] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.268881] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.335621] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.385319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.452972] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.502753] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.552482] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.619412] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.685083] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.735742] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.802496] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.869032] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:07.936158] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.002467] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.068458] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.152652] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.202461] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.286759] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.336563] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.402071] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.469551] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.519006] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.602701] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.668607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.735874] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.801834] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.870043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.919309] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:08.986027] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.052684] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.103005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.169154] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.235832] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.303021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.352298] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.435615] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.485291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.569136] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.636280] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.669300] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.735557] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.802980] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.868667] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.935628] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:09.986313] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.069438] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.136594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.204216] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.252253] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.318766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.386101] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.435616] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:33:10.502731] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:33:13.302285] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-19T07:33:21.289087] [PerformanceMonitoringService] High memory usage detected {"memory_mb":188.0}

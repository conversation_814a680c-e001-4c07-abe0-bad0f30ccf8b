flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:12.841092] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:12.843958] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:12.846576] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #67     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #71     RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #72     RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #73     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #74     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #75     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #76     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #77     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #78     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #79     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #80     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #85     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #86     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #87     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #88     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #89     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #90     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #91     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #92     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #93     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #94     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #95     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #96     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #97     RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #98     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #99     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #100    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #101    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #102    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #103    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #104    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #105    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #106    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #107    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #111    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #112    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #113    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #114    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #115    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #116    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #117    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #118    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #119    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #120    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #121    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #122    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #123    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #124    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #125    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #126    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #127    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #128    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #129    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #130    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #131    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #132    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #133    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #134    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #135    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #136    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #137    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #138    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #139    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #140    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #141    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #142    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #143    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #144    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #145    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #146    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #147    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #148    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #149    _invoke (dart:ui/hooks.dart:312:13)
flutter: #150    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #151    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-19T07:43:12.999931] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-07-19T07:43:13.002103] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-07-19T07:43:13.004473] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-07-19T07:43:13.006861] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-07-19T07:43:13.021981] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: 🐛 DEBUG [2025-07-19T07:43:13.094777] [PerformanceMonitoringService] Slow frame detected {"duration_ms":861}
flutter: ❌ ERROR [2025-07-19T07:43:13.116559] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:13.122130] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:13.126460] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-19T07:43:13.128441] [EnhancedOfflineModeService] Starting offline content sync
flutter: 🐛 DEBUG [2025-07-19T07:43:13.130794] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-07-19T07:43:13.131195] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.176832] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-07-19T07:43:13.397239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.519058] [PerformanceMonitoringService] Slow frame detected {"duration_ms":109}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.543295] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.664976] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.716601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.743502] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.937707] [PerformanceMonitoringService] Slow frame detected {"duration_ms":110}
flutter: 🐛 DEBUG [2025-07-19T07:43:13.965066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":22}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.226528] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.289790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.377246] [PerformanceMonitoringService] Slow frame detected {"duration_ms":87}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.411327] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.444732] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.477042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.527122] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-19T07:43:14.696243] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: Warning: User model is null, user may need to re-authenticate
flutter: 🐛 DEBUG [2025-07-19T07:43:15.062544] [PerformanceMonitoringService] Slow frame detected {"duration_ms":113}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: 🐛 DEBUG [2025-07-19T07:43:15.194239] [PerformanceMonitoringService] Slow frame detected {"duration_ms":152}
flutter: 🐛 DEBUG [2025-07-19T07:43:15.226927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:15.326910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:16.132414] [PerformanceMonitoringService] Slow frame detected {"duration_ms":105}
flutter: 🐛 DEBUG [2025-07-19T07:43:16.159987] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-19T07:43:17.226855] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T07:43:17.925057] [PerformanceMonitoringService] Slow frame detected {"duration_ms":68}
flutter: 🐛 DEBUG [2025-07-19T07:43:17.977433] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.075946] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: ⚠️ WARNING [2025-07-19T07:43:18.141568] [PerformanceMonitoringService] High memory usage detected {"memory_mb":190.0}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.189938] [PerformanceMonitoringService] Slow frame detected {"duration_ms":113}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.227142] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.438798] [PerformanceMonitoringService] Slow frame detected {"duration_ms":212}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.510557] [PerformanceMonitoringService] Slow frame detected {"duration_ms":71}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.593674] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.672286] [PerformanceMonitoringService] Slow frame detected {"duration_ms":78}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.777465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":105}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.861009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.894002] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.949721] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-19T07:43:18.994225] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.093956] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.161657] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.212559] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.277456] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.360505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.461927] [PerformanceMonitoringService] Slow frame detected {"duration_ms":101}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.560820] [PerformanceMonitoringService] Slow frame detected {"duration_ms":98}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.644394] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.684360] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.744713] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.827939] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.893641] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:19.960220] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.027637] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.110199] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.194244] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.261442] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.310671] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.377166] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.460050] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.527335] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.593752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.661209] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.711009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.777439] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.841784] [PerformanceMonitoringService] Slow frame detected {"duration_ms":64}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.927183] [PerformanceMonitoringService] Slow frame detected {"duration_ms":85}
flutter: 🐛 DEBUG [2025-07-19T07:43:20.994752] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:21.087422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:23.760964] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:24.044273] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-19T07:43:29.217099] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-19T07:43:29.244414] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-19T07:43:29.626828] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}

flutter: 🐛 DEBUG [2025-07-19T07:43:31.059081] [PerformanceMonitoringService] Slow frame detected {"duration_ms":65}
flutter: 🐛 DEBUG [2025-07-19T07:43:31.077417] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: Profile screen user data: null
flutter: Profile screen: User model is null
flutter: ❌ ERROR [2025-07-19T07:43:32.411348] [FlutterError] A RenderFlex overflowed by 43 pixels on the bottom. A RenderFlex overflowed by 43 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #66     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #67     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #68     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #69     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #70     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #71     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #72     SchedulerBinding.scheduleWarmUpFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1040:9)
flutter: #73     PlatformDispatcher.scheduleWarmUpFrame.<anonymous closure> (dart:ui/platform_dispatcher.dart:845:16)
flutter: #74     Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
flutter: #75     _Timer._runTimers (dart:isolate-patch/timer_impl.dart:398:19)
flutter: #76     _Timer._handleMessage (dart:isolate-patch/timer_impl.dart:429:5)
flutter: #77     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:184:12)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:32.479315] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
Performing hot reload...                                                
Reloaded 0 libraries in 3,139ms (compile: 1315 ms, reload: 0 ms, reassemble: 1237 ms).
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #66     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #67     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #68     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #69     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #70     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #71     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #72     SchedulerBinding.scheduleWarmUpFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1040:9)
flutter: #73     PlatformDispatcher.scheduleWarmUpFrame.<anonymous closure> (dart:ui/platform_dispatcher.dart:845:16)
flutter: #74     Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
flutter: #75     _Timer._runTimers (dart:isolate-patch/timer_impl.dart:398:19)
flutter: #76     _Timer._handleMessage (dart:isolate-patch/timer_impl.dart:429:5)
flutter: #77     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:184:12)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:32.541270] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #66     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #67     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #68     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #69     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #70     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #71     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #72     SchedulerBinding.scheduleWarmUpFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1040:9)
flutter: #73     PlatformDispatcher.scheduleWarmUpFrame.<anonymous closure> (dart:ui/platform_dispatcher.dart:845:16)
flutter: #74     Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
flutter: #75     _Timer._runTimers (dart:isolate-patch/timer_impl.dart:398:19)
flutter: #76     _Timer._handleMessage (dart:isolate-patch/timer_impl.dart:429:5)
flutter: #77     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:184:12)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:32.543216] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #66     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #67     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #68     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #69     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #70     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #71     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #72     SchedulerBinding.scheduleWarmUpFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1040:9)
flutter: #73     PlatformDispatcher.scheduleWarmUpFrame.<anonymous closure> (dart:ui/platform_dispatcher.dart:845:16)
flutter: #74     Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
flutter: #75     _Timer._runTimers (dart:isolate-patch/timer_impl.dart:398:19)
flutter: #76     _Timer._handleMessage (dart:isolate-patch/timer_impl.dart:429:5)
flutter: #77     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:184:12)
flutter:
flutter: ❌ ERROR [2025-07-19T07:43:32.545468] [FlutterError] A RenderFlex overflowed by 41 pixels on the bottom. A RenderFlex overflowed by 41 pixels on the bottom.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #10     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #11     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #12     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #13     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #14     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #15     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #16     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #17     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #18     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #19     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #20     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #21     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #22     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #23     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #24     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #25     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #26     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #27     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #28     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #29     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #30     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #31     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #32     RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #37     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #38     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #39     RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #40     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #41     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #42     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #43     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #47     RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #48     PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #49     RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #61     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #62     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #63     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #64     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #65     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #66     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #67     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #68     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #69     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #70     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #71     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #72     SchedulerBinding.scheduleWarmUpFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1040:9)
flutter: #73     PlatformDispatcher.scheduleWarmUpFrame.<anonymous closure> (dart:ui/platform_dispatcher.dart:845:16)
flutter: #74     Timer._createTimer.<anonymous closure> (dart:async-patch/timer_patch.dart:18:15)
flutter: #75     _Timer._runTimers (dart:isolate-patch/timer_impl.dart:398:19)
flutter: #76     _Timer._handleMessage (dart:isolate-patch/timer_impl.dart:429:5)
flutter: #77     _RawReceivePort._handleMessage (dart:isolate-patch/isolate_patch.dart:184:12)
flutter:
flutter: 🐛 DEBUG [2025-07-19T07:43:32.577274] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-19T07:43:32.628261] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:43:32.677800] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-19T07:43:32.743859] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-19T07:43:32.906290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-07-19T07:43:32.927143] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-19T07:43:33.642181] [PerformanceMonitoringService] Slow frame detected {"duration_ms":115}
flutter: 🐛 DEBUG [2025-07-19T07:43:33.660542] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: 🐛 DEBUG [2025-07-19T07:43:34.760253] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: ⚠️ WARNING [2025-07-19T07:43:38.097489] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}

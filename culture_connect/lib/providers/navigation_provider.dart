import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum NavigationItem {
  explore,
  arGuide,
  bookings,
  kaia,
  profile,
}

final navigationProvider =
    StateNotifierProvider<NavigationNotifier, NavigationItem>((ref) {
  return NavigationNotifier();
});

class NavigationNotifier extends StateNotifier<NavigationItem> {
  NavigationNotifier() : super(NavigationItem.explore);

  void setNavigationItem(NavigationItem item) {
    state = item;
  }
}

class CustomNavigationDestination {
  final NavigationItem item;
  final String label;
  final IconData icon;
  final IconData selectedIcon;

  const CustomNavigationDestination({
    required this.item,
    required this.label,
    required this.icon,
    required this.selectedIcon,
  });
}

final navigationDestinations = [
  const CustomNavigationDestination(
    item: NavigationItem.explore,
    label: 'Explore',
    icon: Icons.explore_outlined,
    selectedIcon: Icons.explore_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.arGuide,
    label: 'AR Guide',
    icon: Icons.map_outlined,
    selectedIcon: Icons.map_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.bookings,
    label: 'Bookings',
    icon: Icons.calendar_today_outlined,
    selectedIcon: Icons.calendar_today_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.kaia,
    label: 'Kaia',
    icon: Icons.smart_toy_outlined,
    selectedIcon: Icons.smart_toy_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.profile,
    label: 'Profile',
    icon: Icons.person_outline_rounded,
    selectedIcon: Icons.person_rounded,
  ),
];

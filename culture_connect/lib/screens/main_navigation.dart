import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/security_settings.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/providers/security_settings_provider.dart';
import 'package:culture_connect/services/auto_lock_service.dart';
import 'package:culture_connect/widgets/custom_bottom_navigation.dart';
import 'package:culture_connect/widgets/offline/enhanced_offline_status_widget.dart';
import 'package:culture_connect/screens/explore_screen.dart';
import 'package:culture_connect/screens/ar_guide_screen.dart';
import 'package:culture_connect/screens/bookings_screen.dart';
import 'package:culture_connect/screens/kaia_ai_screen.dart';
import 'package:culture_connect/screens/profile_screen.dart';
import 'package:culture_connect/screens/lock_screen.dart';

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({super.key});

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation> {
  bool _isAutoLockInitialized = false;

  void _showLockScreen() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const LockScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
        settings: const RouteSettings(name: '/lock'),
      ),
    );
  }

  void _initializeAutoLockService() {
    if (_isAutoLockInitialized) return;

    final service = ref.read(autoLockServiceProvider);

    // Initialize with current settings and lock callback
    final currentSettings = ref.read(securitySettingsProvider);
    service.initializeWithSettings(currentSettings, _showLockScreen);
    _isAutoLockInitialized = true;
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(navigationProvider);
    final autoLockService = ref.watch(autoLockServiceProvider);

    // Initialize auto-lock service with proper Riverpod pattern
    _initializeAutoLockService();

    // Listen to security settings changes and update auto-lock service
    ref.listen<SecuritySettings>(securitySettingsProvider, (previous, next) {
      if (_isAutoLockInitialized) {
        final service = ref.read(autoLockServiceProvider);
        service.updateSettings(next);
      }
    });

    return GestureDetector(
      // Record user activity on any interaction
      onTap: () => autoLockService.recordActivity(),
      onPanDown: (_) => autoLockService.recordActivity(),
      onScaleStart: (_) => autoLockService.recordActivity(),
      child: Scaffold(
        body: Stack(
          children: [
            IndexedStack(
              index: currentIndex.index,
              children: const [
                ExploreScreen(),
                ARGuideScreen(),
                BookingsScreen(),
                KaiaAIScreen(),
                ProfileScreen(),
              ],
            ),
            // Enhanced offline status banner
            const FloatingOfflineStatusBanner(),
          ],
        ),
        bottomNavigationBar: const CustomBottomNavigation(),
      ),
    );
  }
}

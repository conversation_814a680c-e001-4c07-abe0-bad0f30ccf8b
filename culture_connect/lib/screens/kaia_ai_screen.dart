import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Kaia AI Screen - AI-powered travel assistant
/// Matches React Native Kaia tab functionality
class KaiaAIScreen extends ConsumerStatefulWidget {
  const KaiaAIScreen({super.key});

  @override
  ConsumerState<KaiaAIScreen> createState() => _KaiaAIScreenState();
}

class _KaiaAIScreenState extends ConsumerState<KaiaAIScreen> {
  final TextEditingController _messageController = TextEditingController();
  final List<ChatMessage> _messages = [];
  bool _isTyping = false;

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
  }

  final List<QuickAction> _quickActions = [
    QuickAction(
      id: 'destination',
      title: 'Find Destinations',
      description: 'Discover amazing places to visit based on your preferences',
      icon: Icons.map_outlined,
      prompt:
          'Help me find the perfect travel destination based on my preferences',
      gradient: AppTheme.primaryGradient,
    ),
    QuickAction(
      id: 'itinerary',
      title: 'Plan Itinerary',
      description: 'Create a detailed travel plan with activities and timing',
      icon: Icons.calendar_today_outlined,
      prompt: 'Create a detailed itinerary for my upcoming trip',
      gradient: AppTheme.secondaryGradient,
    ),
    QuickAction(
      id: 'flights',
      title: 'Flight Advice',
      description: 'Get expert tips on finding the best flights and deals',
      icon: Icons.flight_outlined,
      prompt: 'Give me advice on finding the best flights for my trip',
      gradient: AppTheme.accentGradient,
    ),
    QuickAction(
      id: 'accommodation',
      title: 'Hotel Recommendations',
      description: 'Find the perfect stay that matches your style and budget',
      icon: Icons.hotel_outlined,
      prompt: 'Help me find the perfect accommodation for my trip',
      gradient: AppTheme.primaryGradient,
    ),
  ];

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _addWelcomeMessage() {
    _messages.add(
      ChatMessage(
        text:
            "Hi! I'm Kaia, your AI travel assistant. How can I help you plan your next adventure?",
        isUser: false,
        timestamp: DateTime.now(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Quick Actions (show when no messages)
            if (_messages.length <= 1) _buildQuickActions(),

            // Chat Messages
            Expanded(
              child: _buildChatArea(),
            ),

            // Input Area
            _buildInputArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: AppTheme.shadowLight,
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Icon(
              Icons.smart_toy_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMd),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Kaia AI',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeLg,
                    fontWeight: AppTheme.fontWeightBold,
                    color: AppTheme.textPrimaryColor,
                    fontFamily: AppTheme.fontFamily,
                  ),
                ),
                Text(
                  'Your AI Travel Assistant',
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeSm,
                    color: AppTheme.textSecondaryColor,
                    fontFamily: AppTheme.fontFamily,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How can I help you today?',
            style: TextStyle(
              fontSize: AppTheme.fontSizeLg,
              fontWeight: AppTheme.fontWeightBold,
              color: AppTheme.textPrimaryColor,
              fontFamily: AppTheme.fontFamily,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMd),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacingMd,
              mainAxisSpacing: AppTheme.spacingMd,
              childAspectRatio: 1.2,
            ),
            itemCount: _quickActions.length,
            itemBuilder: (context, index) {
              final action = _quickActions[index];
              return GestureDetector(
                onTap: () => _handleQuickAction(action),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: action.gradient,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusMedium),
                    boxShadow: AppTheme.shadowLight,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppTheme.spacingMd),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          action.icon,
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(height: AppTheme.spacingSm),
                        Text(
                          action.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Expanded(
                          child: Text(
                            action.description,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildChatArea() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      itemCount: _messages.length + (_isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _messages.length && _isTyping) {
          return _buildTypingIndicator();
        }

        final message = _messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.smart_toy_rounded,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: AppTheme.spacingSm),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacingMd),
              decoration: BoxDecoration(
                color: message.isUser
                    ? AppTheme.primaryColor
                    : AppTheme.surfaceColor,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusMedium),
                boxShadow: AppTheme.shadowLight,
              ),
              child: Text(
                message.text,
                style: TextStyle(
                  color:
                      message.isUser ? Colors.white : AppTheme.textPrimaryColor,
                  fontSize: AppTheme.fontSizeMd,
                  fontFamily: AppTheme.fontFamily,
                ),
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: AppTheme.spacingSm),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor.withAlpha(50),
              child: Icon(
                Icons.person,
                size: 16,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMd),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.smart_toy_rounded,
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingMd),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              boxShadow: AppTheme.shadowLight,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDot(0),
                const SizedBox(width: 4),
                _buildDot(1),
                const SizedBox(width: 4),
                _buildDot(2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDot(int index) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.4, end: 1.0),
      builder: (context, value, child) {
        return Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: AppTheme.textSecondaryColor.withOpacity(value),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: AppTheme.shadowLight,
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusMedium),
                border: Border.all(color: AppTheme.outline),
              ),
              child: TextField(
                controller: _messageController,
                decoration: const InputDecoration(
                  hintText: 'Ask Kaia anything...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(AppTheme.spacingMd),
                ),
                maxLines: null,
                textCapitalization: TextCapitalization.sentences,
              ),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSm),
          Container(
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
            child: IconButton(
              onPressed: _sendMessage,
              icon: const Icon(
                Icons.send_rounded,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleQuickAction(QuickAction action) {
    // Add the prompt as a user message
    setState(() {
      _messages.add(
        ChatMessage(
          text: action.prompt,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _isTyping = true;
    });

    // Simulate AI response based on action type
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTyping = false;
          _messages.add(
            ChatMessage(
              text: _generateActionResponse(action),
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        });
      }
    });
  }

  String _generateActionResponse(QuickAction action) {
    switch (action.id) {
      case 'destination':
        return "I'd love to help you find the perfect destination! To give you the best recommendations, could you tell me:\n\n• What type of experience are you looking for? (adventure, relaxation, culture, etc.)\n• What's your budget range?\n• When are you planning to travel?\n• Any specific regions or climates you prefer?";
      case 'itinerary':
        return "Great! I can help you create a detailed itinerary. Let me know:\n\n• Your destination\n• Travel dates and duration\n• Your interests (museums, food, nightlife, nature, etc.)\n• Your travel style (fast-paced or relaxed)\n• Any must-see attractions or experiences";
      case 'flights':
        return "I can definitely help you find the best flight deals! Here are some expert tips:\n\n✈️ **Best booking times**: 6-8 weeks in advance for domestic, 2-3 months for international\n✈️ **Flexible dates**: Use fare comparison tools\n✈️ **Consider nearby airports**: Sometimes worth the extra travel\n✈️ **Clear cookies**: Airlines track your searches\n\nWhere are you planning to fly from and to?";
      case 'accommodation':
        return "I'll help you find the perfect place to stay! To give you the best recommendations:\n\n🏨 **What's your preferred accommodation type?** (hotel, Airbnb, hostel, resort)\n🏨 **Location preferences?** (city center, beach, quiet area)\n🏨 **Budget range per night?**\n🏨 **Important amenities?** (pool, gym, kitchen, etc.)\n🏨 **Travel dates?**";
      default:
        return "I'm here to help with your travel planning! What specific aspect would you like assistance with?";
    }
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _messages.add(
        ChatMessage(
          text: text,
          isUser: true,
          timestamp: DateTime.now(),
        ),
      );
      _isTyping = true;
    });

    _messageController.clear();

    // Simulate AI response
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isTyping = false;
          _messages.add(
            ChatMessage(
              text: _generateAIResponse(text),
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        });
      }
    });
  }

  String _generateAIResponse(String userMessage) {
    // Simple AI response simulation
    final responses = [
      "That's a great question! I'd be happy to help you with your travel plans.",
      "Based on your interests, I recommend exploring some cultural experiences.",
      "Let me suggest some amazing destinations that match your preferences.",
      "I can help you find the perfect activities for your trip!",
      "Would you like me to create a personalized itinerary for you?",
    ];

    return responses[DateTime.now().millisecond % responses.length];
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}

class QuickAction {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final String prompt;
  final LinearGradient gradient;

  const QuickAction({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.prompt,
    required this.gradient,
  });
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Airbnb-Style Color Palette (React Native Design System)
  // Primary brand colors - Airbnb signature red/pink
  static const Color primaryColor = Color(0xFFFF385C); // Airbnb red
  static const Color primaryLight = Color(0xFFFF5A75); // Light red
  static const Color primaryDark = Color(0xFFE8294A); // Dark red

  // Secondary colors
  static const Color secondaryColor = Color(0xFF00A699); // Teal
  static const Color accentColor = Color(0xFFFC642D); // Orange

  // Neutral colors
  static const Color backgroundColor = Color(0xFFFFFFFF); // White
  static const Color backgroundSecondary = Color(0xFFF7F7F7);
  static const Color backgroundTertiary = Color(0xFFF0F0F0);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF7F7F7);
  static const Color outline = Color(0xFFDDDDDD);
  static const Color outlineVariant = Color(0xFFEBEBEB);

  // Text colors
  static const Color textPrimaryColor = Color(0xFF222222); // Dark gray
  static const Color textSecondaryColor = Color(0xFF717171); // Gray
  static const Color textTertiaryColor = Color(0xFFB0B0B0); // Light gray
  static const Color textInverse = Color(0xFFFFFFFF); // White text

  // UI colors
  static const Color borderColor = Color(0xFFDDDDDD); // Light gray border
  static const Color borderLight = Color(0xFFEBEBEB);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);

  // Status colors
  static const Color successColor =
      Color(0xFF00A699); // Using secondary for success
  static const Color errorColor = Color(0xFFC13515); // Red
  static const Color warningColor =
      Color(0xFFFC642D); // Using accent for warnings
  static const Color infoColor = Color(0xFF00A699); // Using secondary for info

  // Additional Colors
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color dividerColor = Color(0xFFDDDDDD);
  static const Color shadowColor = Color(0x1F222222); // rgba(34, 34, 34, 0.12)
  static const Color shadowDark = Color(0x40222222); // rgba(34, 34, 34, 0.25)
  static const Color overlayColor = Color(0x80222222); // rgba(34, 34, 34, 0.5)
  static const Color overlayLight = Color(0x4D222222); // rgba(34, 34, 34, 0.3)

  // Spacing System (React Native Design System)
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;

  // Typography System (React Native Design System)
  static const String fontFamily = 'System'; // Use default system font

  // Font Sizes
  static const double fontSizeXs = 12.0;
  static const double fontSizeSm = 14.0;
  static const double fontSizeMd = 16.0;
  static const double fontSizeLg = 18.0;
  static const double fontSizeXl = 20.0;
  static const double fontSizeXxl = 28.0;

  // Font Weights
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemibold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;

  // Line Heights
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.4;
  static const double lineHeightRelaxed = 1.6;

  // Border Radius System (React Native Design System)
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 20.0;

  // Modern Gradients - Airbnb style
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, primaryDark],
    stops: [0.0, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryColor, Color(0xFF008B7A)], // Darker teal
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentColor, Color(0xFFE55A26)], // Darker orange
    stops: [0.0, 1.0],
  );

  // New modern gradients for enhanced UI
  static const LinearGradient heroGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF667EEA),
      Color(0xFF764BA2),
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFFFFF),
      Color(0xFFF8FAFC),
    ],
    stops: [0.0, 1.0],
  );

  // Coral/Salmon gradient for auth screens
  static const LinearGradient coralGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFFFF9A8B), // Light coral
      Color(0xFFFF6B6B), // Medium coral
      Color(0xFFFF5252), // Deeper coral
    ],
    stops: [0.0, 0.6, 1.0],
  );

  // Auth screen colors
  static const Color authBackgroundStart = Color(0xFFFF9A8B);
  static const Color authBackgroundEnd = Color(0xFFFF5252);
  static const Color authContentBackground = Color(0xFFFFFFFF);
  static const Color authButtonColor = Color(0xFFFF6B6B);

  // Card Decoration
  static BoxDecoration cardDecoration = BoxDecoration(
    color: cardColor,
    borderRadius: BorderRadius.circular(16),
    boxShadow: const [
      BoxShadow(
        color: shadowColor,
        blurRadius: 10,
        offset: Offset(0, 4),
      ),
    ],
  );

  // Shadow System (React Native Design System)
  static const List<BoxShadow> shadowLight = [
    BoxShadow(
      color: Color(0x1A222222), // shadowColor with 0.1 opacity
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> shadowMedium = [
    BoxShadow(
      color: Color(0x26222222), // shadowColor with 0.15 opacity
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> shadowHeavy = [
    BoxShadow(
      color: Color(0x26222222), // shadowColor with 0.15 opacity
      offset: Offset(0, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];

  // Animation Constants (React Native Design System)
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 600);

  // Spring animation parameters
  static const double springDamping = 15.0;
  static const double springStiffness = 300.0;

  // Scale animation values
  static const double scalePressed = 0.95;
  static const double scaleHover = 1.02;
  static const double scaleNormal = 1.0;

  // Button Styles (Airbnb Design System)
  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadiusMedium),
    ),
    elevation: 2,
  );

  static ButtonStyle secondaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: secondaryColor,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadiusMedium),
    ),
    elevation: 2,
  );

  static ButtonStyle outlinedButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: primaryColor,
    side: const BorderSide(color: primaryColor, width: 2),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadiusMedium),
    ),
  );

  static ButtonStyle textButtonStyle = TextButton.styleFrom(
    foregroundColor: primaryColor,
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadiusSmall),
    ),
  );

  // Input Decoration
  static InputDecoration inputDecoration({
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: surfaceColor,
      labelStyle: const TextStyle(color: textSecondaryColor),
      hintStyle: const TextStyle(color: textSecondaryColor),
      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: errorColor, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: errorColor, width: 2),
      ),
    );
  }

  // Dark Theme Colors
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkSurfaceColor = Color(0xFF1E1E1E);
  static const Color darkTextPrimaryColor = Color(0xFFE0E0E0);
  static const Color darkTextSecondaryColor = Color(0xFFB0B0B0);

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 800);

  // Micro-Interaction Animation Durations
  static const Duration buttonPressAnimation = Duration(milliseconds: 150);
  static const Duration buttonReleaseAnimation = Duration(milliseconds: 200);
  static const Duration rippleAnimation = Duration(milliseconds: 300);
  static const Duration loadingAnimation = Duration(milliseconds: 1200);
  static const Duration successAnimation = Duration(milliseconds: 600);
  static const Duration errorAnimation = Duration(milliseconds: 400);

  // Haptic Feedback Intensities
  static const String lightHaptic = 'light';
  static const String mediumHaptic = 'medium';
  static const String heavyHaptic = 'heavy';

  // Button Interaction Constants
  static const double buttonPressScale = 0.95;
  static const double buttonHoverScale = 1.02;
  static const double rippleOpacityLight = 0.1;
  static const double rippleOpacityMedium = 0.2;
  static const double rippleOpacityHeavy = 0.3;

  // Button State Colors (Alpha values for withAlpha())
  static const int buttonPressedAlpha = 204; // 0.8
  static const int buttonHoverAlpha = 26; // 0.1
  static const int buttonDisabledAlpha = 77; // 0.3
  static const int buttonLoadingAlpha = 153; // 0.6

  // Additional border radius for rounded buttons
  static const double borderRadiusRounded = 999.0; // For pill-shaped buttons

  // Legacy spacing aliases for backward compatibility
  static const double spacingSmall = spacingSm;
  static const double spacingMedium = spacingMd;
  static const double spacingLarge = spacingLg;
  static const double spacingXLarge = spacingXl;
  static const double spacingXXLarge = spacingXxl;

  // Modern Shadow System - More subtle and layered
  static List<BoxShadow> get shadowXS => [
        BoxShadow(
          color: Colors.black.withAlpha(13), // 0.05 opacity
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ];

  static List<BoxShadow> get shadowSmall => [
        BoxShadow(
          color: Colors.black.withAlpha(26), // 0.1 opacity
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
        BoxShadow(
          color: Colors.black.withAlpha(13), // 0.05 opacity
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ];

  // Additional shadow definitions for compatibility
  static List<BoxShadow> get shadowLarge => shadowHeavy;
  static List<BoxShadow> get shadowXLarge => [
        BoxShadow(
          color: Colors.black.withAlpha(51), // 0.2 opacity
          blurRadius: 24,
          offset: const Offset(0, 12),
        ),
        BoxShadow(
          color: Colors.black.withAlpha(38), // 0.15 opacity
          blurRadius: 16,
          offset: const Offset(0, 8),
        ),
      ];

  // Additional animation durations for compatibility
  static const Duration animationNormal = Duration(milliseconds: 250);
  static const Duration animationSlower = Duration(milliseconds: 600);

  // Animation Curves - Modern and smooth
  static const Curve curveDefault = Curves.easeInOut;
  static const Curve curveEmphasized = Curves.easeInOutCubic;
  static const Curve curveDecelerated = Curves.easeOut;
  static const Curve curveAccelerated = Curves.easeIn;

  // Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        error: errorColor,
      ),
      textTheme: GoogleFonts.poppinsTextTheme(
        ThemeData.light().textTheme.copyWith(
              displayLarge: const TextStyle(
                color: textPrimaryColor,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
              displayMedium: const TextStyle(
                color: textPrimaryColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              displaySmall: const TextStyle(
                color: textPrimaryColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              bodyLarge: const TextStyle(
                color: textPrimaryColor,
                fontSize: 16,
              ),
              bodyMedium: const TextStyle(
                color: textPrimaryColor,
                fontSize: 14,
              ),
              bodySmall: const TextStyle(
                color: textSecondaryColor,
                fontSize: 12,
              ),
            ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: textPrimaryColor),
        titleTextStyle: TextStyle(
          color: textPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingMd,
            vertical: spacingSm,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: const EdgeInsets.symmetric(
            horizontal: spacingMd,
            vertical: spacingSm,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: spacingMd,
            vertical: spacingSm,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingSmall,
        ),
      ),
      cardTheme: CardTheme(
        color: backgroundColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: darkSurfaceColor,
        error: errorColor,
      ),
      textTheme: GoogleFonts.poppinsTextTheme(
        ThemeData.dark().textTheme.copyWith(
              displayLarge: const TextStyle(
                color: darkTextPrimaryColor,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
              displayMedium: const TextStyle(
                color: darkTextPrimaryColor,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              displaySmall: const TextStyle(
                color: darkTextPrimaryColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              bodyLarge: const TextStyle(
                color: darkTextPrimaryColor,
                fontSize: 16,
              ),
              bodyMedium: const TextStyle(
                color: darkTextPrimaryColor,
                fontSize: 14,
              ),
              bodySmall: const TextStyle(
                color: darkTextSecondaryColor,
                fontSize: 12,
              ),
            ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: darkBackgroundColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: IconThemeData(color: darkTextPrimaryColor),
        titleTextStyle: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: EdgeInsets.symmetric(
            horizontal: spacingMedium,
            vertical: spacingSmall,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: EdgeInsets.symmetric(
            horizontal: spacingMedium,
            vertical: spacingSmall,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: EdgeInsets.symmetric(
            horizontal: spacingMedium,
            vertical: spacingSmall,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingSmall,
        ),
      ),
      cardTheme: CardTheme(
        color: darkSurfaceColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
      ),
    );
  }
}

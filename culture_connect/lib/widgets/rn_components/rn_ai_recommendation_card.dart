import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native AIRecommendationCard replica
/// Matches the exact styling from React Native reference images
class RNAIRecommendationCard extends StatelessWidget {
  final String title;
  final String description;
  final String imageUrl;
  final VoidCallback onPressed;

  const RNAIRecommendationCard({
    super.key,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with AI Badge
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // AI Badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryColor.withAlpha(204), // 80% opacity
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.auto_awesome,
                          size: 12,
                          color: AppTheme.white,
                        ),
                        const SizedBox(width: 4),
                        const Text(
                          'AI Recommended',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: AppTheme.fontWeightSemibold,
                            color: AppTheme.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Image Section
            ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: Stack(
                children: [
                  Container(
                    height: 140,
                    width: double.infinity,
                    color: AppTheme.backgroundSecondary,
                    child: imageUrl.isNotEmpty
                        ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: AppTheme.backgroundSecondary,
                                child: const Icon(
                                  Icons.image_outlined,
                                  size: 48,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              );
                            },
                          )
                        : const Icon(
                            Icons.image_outlined,
                            size: 48,
                            color: AppTheme.textSecondaryColor,
                          ),
                  ),
                  
                  // Content Overlay
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            AppTheme.black.withAlpha(153), // 60% opacity
                          ],
                        ),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: AppTheme.fontSizeLg, // 18px
                              fontWeight: AppTheme.fontWeightBold,
                              color: AppTheme.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 4),
                          
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: AppTheme.fontSizeSm, // 14px
                              color: AppTheme.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          
                          const SizedBox(height: 8),
                          
                          // View Details Button
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: AppTheme.white.withAlpha(230), // 90% opacity
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Text(
                              'View Details',
                              style: TextStyle(
                                fontSize: AppTheme.fontSizeXs, // 12px
                                fontWeight: AppTheme.fontWeightSemibold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

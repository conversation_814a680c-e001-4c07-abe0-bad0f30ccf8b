import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native PromotionalBanner replica
/// Matches the exact styling from React Native reference images
class RNPromotionalBanner extends StatelessWidget {
  final String title;
  final String subtitle;
  final String imageUrl;
  final VoidCallback onPressed;

  const RNPromotionalBanner({
    super.key,
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: 160,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: AppTheme.shadowColor,
              offset: const Offset(0, 2),
              blurRadius: 8,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: <PERSON>ack(
            children: [
              // Background Image
              Positioned.fill(
                child: imageUrl.isNotEmpty
                    ? Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.primaryColor,
                            child: const Icon(
                              Icons.image_outlined,
                              size: 48,
                              color: AppTheme.white,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: AppTheme.primaryColor,
                        child: const Icon(
                          Icons.image_outlined,
                          size: 48,
                          color: AppTheme.white,
                        ),
                      ),
              ),
              
              // Gradient Overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        AppTheme.black.withAlpha(153), // 60% opacity
                        AppTheme.black.withAlpha(77),  // 30% opacity
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),
              
              // Content
              Positioned(
                left: 24,
                top: 0,
                bottom: 0,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: AppTheme.fontSizeMd, // 16px
                        fontWeight: AppTheme.fontWeightMedium,
                        color: AppTheme.white,
                      ),
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: AppTheme.fontSizeXl, // 20px
                        fontWeight: AppTheme.fontWeightBold,
                        color: AppTheme.white,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // CTA Button
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Text(
                        'Explore Now',
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeSm, // 14px
                          fontWeight: AppTheme.fontWeightSemibold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Pixel-perfect React Native QuickActionButton replica
/// Matches the exact styling from React Native reference images
class RNQuickActionButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final VoidCallback onPressed;

  const RNQuickActionButton({
    super.key,
    required this.title,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 80, // Fixed width for consistent grid
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon Container
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(16), // Rounded square
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.shadowColor,
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 24,
                color: iconColor,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Title
            Text(
              title,
              style: const TextStyle(
                fontFamily: AppTheme.fontFamily,
                fontSize: AppTheme.fontSizeXs, // 12px
                fontWeight: AppTheme.fontWeightMedium,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// Quick Action Data Model
class QuickActionData {
  final String title;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final String action;

  const QuickActionData({
    required this.title,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.action,
  });
}

/// Predefined Quick Actions matching React Native reference
class RNQuickActions {
  static const List<QuickActionData> actions = [
    QuickActionData(
      title: 'Hotels',
      icon: Icons.hotel_outlined,
      backgroundColor: Color(0xFFFFF5F5), // Light red background
      iconColor: AppTheme.primaryColor, // #FF385C
      action: 'hotel',
    ),
    QuickActionData(
      title: 'Tours',
      icon: Icons.map_outlined,
      backgroundColor: Color(0xFFFFFBEB), // Light yellow background
      iconColor: Color(0xFFF59E0B), // Orange
      action: 'tours',
    ),
    QuickActionData(
      title: 'Flights',
      icon: Icons.flight_outlined,
      backgroundColor: Color(0xFFF0FDFA), // Light teal background
      iconColor: AppTheme.secondaryColor, // #00A699
      action: 'flights',
    ),
    QuickActionData(
      title: 'Cars',
      icon: Icons.directions_car_outlined,
      backgroundColor: Color(0xFFFFF7ED), // Light orange background
      iconColor: AppTheme.accentColor, // #FC642D
      action: 'cars',
    ),
    QuickActionData(
      title: 'Luxury',
      icon: Icons.diamond_outlined,
      backgroundColor: Color(0xFFFAF5FF), // Light purple background
      iconColor: Color(0xFFD946EF), // Purple
      action: 'luxury',
    ),
    QuickActionData(
      title: 'Food',
      icon: Icons.restaurant_outlined,
      backgroundColor: Color(0xFFFFFBEB), // Light yellow background
      iconColor: AppTheme.warningColor, // #FC642D
      action: 'food',
    ),
    QuickActionData(
      title: 'Visa',
      icon: Icons.description_outlined,
      backgroundColor: Color(0xFFF3F4F6), // Light gray background
      iconColor: Color(0xFF8B5CF6), // Purple
      action: 'visa',
    ),
    QuickActionData(
      title: 'Medical',
      icon: Icons.local_hospital_outlined,
      backgroundColor: Color(0xFFFEF2F2), // Light red background
      iconColor: Color(0xFFEF4444), // Red
      action: 'medical',
    ),
  ];
}
